import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Global variable to store results for the validation report
let lastPhase2Results = null;

// Plastic name mapping - maps your plastic names to Innova's plastic names
const plasticMapping = {
    // Your plastic name: Innova plastic name
    'KC Pro': 'Pro KC',
    'Pro KC': 'Pro <PERSON>',
    'Champion': 'Champion',
    'Star': 'Star',
    'DX': 'DX',
    'Pro': 'Pro',
    'XT': 'XT',
    'GStar': 'G-Star',
    'Gstar': 'G-Star',
    'G-Star': 'G-Star',
    'R-Pro': 'R-Pro',
    'Nexus': 'Nexus',
    
    // Glow plastic mappings
    'DX Glow': 'DX Classic Glow',
    'Champion Glow': 'Champion Classic Glow',
    'Proto Glow Champion': 'Champion Proto Glow',
    'Proto Glow DX': 'DX Proto Glow',
    
    // Halo plastic mappings
    'Halo Star': 'Halo Star',
    'Halo Nexus': 'Halo Nexus',
    'Proto Glow Halo Star': 'Halo Proto Glow Star',
    
    // Overmold and INNfuse plastic mappings
    'Overmold Champion': 'Overmold Champion',
    'Overmold Star INNfuse': 'INNfuse Star',
    'INNfuse Star': 'INNfuse Star',
    'Innfuse': 'INNfuse Star',
    
    // Blizzard plastic mappings
    'Champion Blizzard': 'Blizzard Champion',
    
    // Direct matches (for clarity)
    'R-Pro': 'R-Pro'
};

// Mold name mapping - maps your mold names to Innova's mold names
const moldMapping = {
    // Your mold name: Innova mold name
    'AviardX3': 'Aviar X3',
    'AviarX3': 'Aviar X3',
    'Aviar3': 'Aviar3',
    'Mako3': 'Mako 3',
    'Aviar Classic': 'Classic Aviar',
    'Aviar Putter': 'Aviar',
    'Aviar P&A': 'Aviar',
    'Leopard3': 'Leopard 3',
    'Teebird3': 'Teebird 3',
    'Roc3': 'Roc 3',
    'TL3': 'TL 3'
};

// Normalize plastic name for comparison
function normalizePlasticName(plasticName) {
    if (!plasticName) return null;
    const mapped = plasticMapping[plasticName];
    if (mapped) return mapped;
    return plasticName;
}

// Normalize mold name for comparison
function normalizeMoldName(moldName) {
    if (!moldName) return null;
    const mapped = moldMapping[moldName];
    if (mapped) return mapped;
    return moldName;
}

// Parse Innova description to extract mold and plastic
function parseInnovaDescription(description) {
    if (!description) return null;
    
    // Common Innova plastic types (order matters - longer names first)
    const plasticTypes = [
        'Overmold Star INNfuse', 'INNfuse Star', 'Overmold Champion', 'Overmold',
        'Champion I-Dye', 'Champion Proto Glow', 'Champion Classic Glow', 'Blizzard Champion', 'Champion', 
        'Star I-Dye', 'Halo Star', 'Star', 
        'DX Proto Glow', 'DX Classic Glow', 'DX', 
        'Pro KC', 'KC Pro', 'R-Pro', 'Pro',
        'Halo Nexus', 'Nexus',
        'Metal Flake Champion', 'GStar', 'G-Star', 'XT'
    ];
    
    let plastic = null;
    let remainingText = description.trim();
    
    // Find plastic type
    for (const plasticType of plasticTypes) {
        // Use word boundaries to prevent partial matches (e.g., "Pro" in "Approach")
        const regex = new RegExp(`\\b${plasticType}\\b`, 'gi');
        if (regex.test(description)) {
            plastic = plasticType;
            // Remove plastic from description to get mold
            remainingText = description.replace(regex, '').trim();
            break;
        }
    }
    
    // Extract mold name (remove Innova disc type classifications - these are ignored for matching)
    let mold = remainingText
        .replace(/\s+(Putt & Approach|Fairway Distance Driver|Distance Driver|Fairway Driver|Mid-Range|Midrange|Specialty Disc|Putter|Driver|Approach)(\s+Disc)?$/i, '')
        .replace(/\s+Disc$/i, '') // Remove standalone "Disc" at the end
        .replace(/\s+with\s+.*/i, '') // Remove "with Burst Logo" etc
        .replace(/\s+Overmold\s*-?\s*$/i, '') // Remove "Overmold" and trailing dashes
        .replace(/\s*-\s*$/i, '') // Remove trailing dashes
        .trim();
    
    return {
        plastic: plastic,
        mold: mold,
        original: description
    };
}

// Parse weight range from Innova matrix_option_1
function parseInnovaWeights(weightStr) {
    if (!weightStr) return null;
    
    const str = weightStr.toString().toLowerCase().trim();
    
    // Handle "max weight" or similar
    if (str.includes('max') || str.includes('minimum') || str.includes('varies')) {
        return { error: 'unparseable', original: weightStr };
    }
    
    // Handle "<139" format (means up to 139g, typically 100-139g for discs)
    const lessThanMatch = str.match(/^<\s*(\d+)/);
    if (lessThanMatch) {
        const maxWeight = parseInt(lessThanMatch[1]);
        return {
            min: 100, // Assume minimum disc weight is 100g
            max: maxWeight,
            original: weightStr,
            note: 'parsed_less_than'
        };
    }
    
    // Handle ">175" format (means 175g and up, typically 175-180g for discs)
    const greaterThanMatch = str.match(/^>\s*(\d+)/);
    if (greaterThanMatch) {
        const minWeight = parseInt(greaterThanMatch[1]);
        return {
            min: minWeight,
            max: 180, // Assume maximum disc weight is 180g
            original: weightStr,
            note: 'parsed_greater_than'
        };
    }
    
    // Look for pattern like "173-175" or "173-175g"
    const rangeMatch = str.match(/(\d+)\s*-\s*(\d+)/);
    if (rangeMatch) {
        return {
            min: parseInt(rangeMatch[1]),
            max: parseInt(rangeMatch[2]),
            original: weightStr
        };
    }
    
    // Look for single number like "175g" or "175"
    const singleMatch = str.match(/(\d+)/);
    if (singleMatch) {
        const weight = parseInt(singleMatch[1]);
        return {
            min: weight,
            max: weight,
            original: weightStr
        };
    }
    
    return { error: 'unparseable', original: weightStr };
}

// Calculate weight match type
function calculateWeightMatch(oslMin, oslMax, innovaWeights) {
    if (!innovaWeights || innovaWeights.error) {
        return 'UNPARSEABLE';
    }
    
    const innovaMin = innovaWeights.min;
    const innovaMax = innovaWeights.max;
    
    // Check for exact match
    if (oslMin === innovaMin && oslMax === innovaMax) {
        return 'EXACT_MATCH';
    }
    
    // Check for overlap
    if (oslMin <= innovaMax && oslMax >= innovaMin) {
        return 'OVERLAP';
    }
    
    return 'NO_OVERLAP';
}

// Calculate string similarity (simple implementation)
function calculateStringSimilarity(str1, str2) {
    if (!str1 || !str2) return 0;
    
    const s1 = str1.toLowerCase();
    const s2 = str2.toLowerCase();
    
    if (s1 === s2) return 100;
    
    // Simple character-based similarity
    const longer = s1.length > s2.length ? s1 : s2;
    const shorter = s1.length > s2.length ? s2 : s1;
    
    if (longer.length === 0) return 100;
    
    const matches = shorter.split('').filter(char => longer.includes(char)).length;
    return Math.round((matches / longer.length) * 100);
}

// Main Phase 2 matching function
async function runPhase2Matching() {
    try {
        console.log('Starting Phase 2 Innova matching (unmatched OSLs)...');

        // Get unmatched OSLs with their MPS data
        const { data: oslData, error: oslError } = await supabase
            .from('t_order_sheet_lines')
            .select(`
                id, g_code, min_weight, max_weight, color_id, mps_id,
                t_mps!inner (
                    id, g_code,
                    t_molds!inner (id, mold),
                    t_plastics!inner (id, plastic),
                    t_stamps!inner (id, stamp)
                ),
                t_colors!inner (id, color)
            `)
            .eq('vendor_id', 2)
            .is('vendor_internal_id', null) // Only unmatched OSLs
            .limit(100); // Start with first 100 for testing

        if (oslError) {
            throw new Error(`Error fetching unmatched OSL data: ${oslError.message}`);
        }

        console.log(`Found ${oslData.length} unmatched Innova OSLs`);

        // Get all Innova data with chunking
        console.log('Fetching all Innova data...');
        let innovaData = [];
        let offset = 0;
        const chunkSize = 1000;

        while (true) {
            const { data: chunk, error: chunkError } = await supabase
                .from('it_innova_order_sheet_lines')
                .select('internal_id, description, matrix_option_1')
                .not('matrix_option_1', 'is', null) // Exclude parent records
                .range(offset, offset + chunkSize - 1);

            if (chunkError) {
                throw new Error(`Error fetching Innova data chunk: ${chunkError.message}`);
            }

            if (!chunk || chunk.length === 0) {
                break;
            }

            innovaData = innovaData.concat(chunk);
            offset += chunkSize;

            if (chunk.length < chunkSize) {
                break;
            }
        }

        console.log(`Found ${innovaData.length} total Innova records`);

        // Get rejected matches to exclude them
        console.log('Loading rejected matches...');
        const { data: rejectedMatches, error: rejectedError } = await supabase
            .from('t_vendor_match_rejections')
            .select('osl_id, vendor_internal_id')
            .eq('vendor_id', 2);

        if (rejectedError && !rejectedError.message.includes('does not exist')) {
            console.warn('Could not load rejected matches:', rejectedError.message);
        }

        // Create a Set for fast lookup of rejected combinations
        const rejectedSet = new Set();
        if (rejectedMatches) {
            rejectedMatches.forEach(rejection => {
                rejectedSet.add(`${rejection.osl_id}-${rejection.vendor_internal_id}`);
            });
            console.log(`Loaded ${rejectedMatches.length} rejected match combinations`);
        }

        // Phase 2 matching results
        const phase2Results = [];
        let processedCount = 0;

        // Process each unmatched OSL
        for (const osl of oslData) {
            processedCount++;
            console.log(`Processing OSL ${osl.id} (${processedCount}/${oslData.length})...`);

            const candidates = [];

            // Compare against all Innova records
            for (const innovaRecord of innovaData) {
                // Skip if this combination has been rejected
                const rejectionKey = `${osl.id}-${innovaRecord.internal_id}`;
                if (rejectedSet.has(rejectionKey)) {
                    continue;
                }

                const innovaParsed = parseInnovaDescription(innovaRecord.description);
                const innovaWeights = parseInnovaWeights(innovaRecord.matrix_option_1);

                // Calculate matches
                const oslMoldNormalized = normalizeMoldName(osl.t_mps.t_molds.mold);
                const innovaMoldNormalized = normalizeMoldName(innovaParsed?.mold);
                const moldMatch = oslMoldNormalized?.toLowerCase() === innovaMoldNormalized?.toLowerCase();

                const oslPlasticNormalized = normalizePlasticName(osl.t_mps.t_plastics.plastic);
                const innovaPlasticNormalized = normalizePlasticName(innovaParsed?.plastic);
                const plasticMatch = oslPlasticNormalized?.toLowerCase() === innovaPlasticNormalized?.toLowerCase();

                const weightMatch = calculateWeightMatch(osl.min_weight, osl.max_weight, innovaWeights);

                // Calculate confidence score with weight penalty for non-overlapping weights
                let confidenceScore = 0;
                if (moldMatch) confidenceScore += 40;
                if (plasticMatch) confidenceScore += 40;
                if (weightMatch === 'EXACT_MATCH') confidenceScore += 20;
                else if (weightMatch === 'OVERLAP') confidenceScore += 10;
                else if (weightMatch === 'NO_OVERLAP') {
                    // Heavy penalty for non-overlapping weights when mold+plastic match
                    if (moldMatch && plasticMatch) {
                        confidenceScore -= 30; // Reduce confidence significantly
                    }
                }

                // Add string similarity bonus
                const descSimilarity = calculateStringSimilarity(
                    `${osl.t_mps.t_molds.mold} ${osl.t_mps.t_plastics.plastic}`,
                    innovaRecord.description
                );
                confidenceScore += Math.round(descSimilarity * 0.2); // Up to 20 points

                // Ensure score doesn't go below 0
                confidenceScore = Math.max(0, confidenceScore);

                // Generate status based on confidence score
                let status;
                if (confidenceScore >= 90) status = '🟢 PERFECT_MATCH';
                else if (confidenceScore >= 80) status = '🟢 HIGH_CONFIDENCE';
                else if (confidenceScore >= 70) status = '🟡 MEDIUM_CONFIDENCE';
                else if (confidenceScore >= 60) status = '🟠 LOW_CONFIDENCE';
                else status = '🔴 POOR_MATCH';

                // Only keep candidates above 50% confidence
                if (confidenceScore >= 50) {
                    candidates.push({
                        innova_internal_id: innovaRecord.internal_id,
                        innova_description: innovaRecord.description,
                        innova_parsed: innovaParsed,
                        innova_weights: innovaWeights?.error ? innovaWeights.original :
                            `${innovaWeights?.min}-${innovaWeights?.max}${innovaWeights?.note ? ` (${innovaWeights.note})` : ''}`,
                        mold_match: moldMatch,
                        plastic_match: plasticMatch,
                        weight_match: weightMatch,
                        confidence_score: Math.min(confidenceScore, 100),
                        string_similarity: descSimilarity,
                        status: status
                    });
                }
            }

            // Sort by confidence score (highest first) and take top 3
            candidates.sort((a, b) => b.confidence_score - a.confidence_score);
            const topCandidates = candidates.slice(0, 3);

            // Add OSL with its top candidates
            if (topCandidates.length > 0) {
                topCandidates.forEach((candidate, index) => {
                    phase2Results.push({
                        osl_id: osl.id,
                        mps_id: osl.mps_id,
                        candidate_rank: index + 1, // Rank 1 = highest score
                        osl_mold: osl.t_mps.t_molds.mold,
                        osl_plastic: osl.t_mps.t_plastics.plastic,
                        osl_stamp: osl.t_mps.t_stamps.stamp,
                        osl_weights: `${osl.min_weight}-${osl.max_weight}`,
                        osl_color: osl.t_colors.color,
                        // Add parsed data for training
                        innova_parsed_mold: candidate.innova_parsed?.mold || 'PARSE_FAILED',
                        innova_parsed_plastic: candidate.innova_parsed?.plastic || 'PARSE_FAILED',
                        osl_mold_normalized: normalizeMoldName(osl.t_mps.t_molds.mold),
                        osl_plastic_normalized: normalizePlasticName(osl.t_mps.t_plastics.plastic),
                        innova_mold_normalized: normalizeMoldName(candidate.innova_parsed?.mold),
                        innova_plastic_normalized: normalizePlasticName(candidate.innova_parsed?.plastic),
                        ...candidate
                    });
                });
            }
        }

        // Store results globally
        lastPhase2Results = phase2Results;

        console.log('\n=== PHASE 2 MATCHING RESULTS ===');
        console.log(`OSLs processed: ${oslData.length}`);
        console.log(`Potential matches found: ${phase2Results.length}`);
        console.log(`OSLs with matches: ${new Set(phase2Results.map(r => r.osl_id)).size}`);

        const highConfidence = phase2Results.filter(r => r.confidence_score >= 80).length;
        const mediumConfidence = phase2Results.filter(r => r.confidence_score >= 70 && r.confidence_score < 80).length;
        const lowConfidence = phase2Results.filter(r => r.confidence_score >= 50 && r.confidence_score < 70).length;

        console.log(`High confidence (80%+): ${highConfidence}`);
        console.log(`Medium confidence (70-79%): ${mediumConfidence}`);
        console.log(`Low confidence (50-69%): ${lowConfidence}`);

        // Show first few results
        console.log('\nFirst 3 potential matches:');
        phase2Results.slice(0, 3).forEach(result => {
            console.log(`${result.candidate_rank}. OSL ${result.osl_id}: ${result.osl_mold} ${result.osl_plastic} ↔ ${result.innova_description} (${result.confidence_score}%)`);
        });

        return {
            success: true,
            processed: oslData.length,
            matches: phase2Results.length,
            stats: { highConfidence, mediumConfidence, lowConfidence }
        };

    } catch (error) {
        console.error('Phase 2 matching failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Function to get the stored Phase 2 results
function getPhase2Results() {
    return lastPhase2Results;
}

export { runPhase2Matching, getPhase2Results };

// If running directly, execute the matching
if (import.meta.url === `file://${process.argv[1]}`) {
    console.log('Running Phase 2 matching directly...');
    runPhase2Matching().then(result => {
        if (result.success) {
            console.log('✅ Phase 2 matching completed successfully!');
        } else {
            console.error('❌ Phase 2 matching failed:', result.error);
        }
    });
}
